//
//  BrowserManager.swift
//  cop
//
//  Created by Augment Agent on 2025/6/15.
//

import Foundation
import WebKit
import Network
import OSLog

// MARK: - 简化的浏览器性能指标
struct BrowserMetrics {
    var activeWebViews: Int = 0
    var memoryUsage: UInt64 = 0
    var networkRequests: Int = 0
    var errorCount: Int = 0
    
    var description: String {
        return "WebViews: \(activeWebViews), Memory: \(ByteCountFormatter.string(fromByteCount: Int64(memoryUsage), countStyle: .memory))"
    }
    
    var healthStatus: String {
        if memoryUsage > 300 * 1024 * 1024 { // 300MB for iPad mini A17 Pro
            return "内存使用过高"
        } else if errorCount > 5 {
            return "网络不稳定"
        } else {
            return "运行正常"
        }
    }
}

// MARK: - 网络质量枚举
enum NetworkQuality: String, CaseIterable {
    case excellent = "excellent"
    case good = "good"
    case fair = "fair"
    case poor = "poor"
    
    var description: String {
        switch self {
        case .excellent: return "优秀"
        case .good: return "良好"
        case .fair: return "一般"
        case .poor: return "较差"
        }
    }
}

// MARK: - 连接类型
enum ConnectionType {
    case wifi, cellular, ethernet, unknown
    
    var displayName: String {
        switch self {
        case .wifi: return "WiFi"
        case .cellular: return "蜂窝网络"
        case .ethernet: return "以太网"
        case .unknown: return "未知"
        }
    }
}

// MARK: - 网络统计
struct NetworkStats {
    var totalRequests: Int = 0
    var successfulRequests: Int = 0
    var averageResponseTime: TimeInterval = 0
    var successRate: Double = 0
}

/// 统一的浏览器管理器 - 整合WebView管理、网络监控和错误处理
@MainActor
final class BrowserManager: ObservableObject {
    static let shared = BrowserManager()
    
    // MARK: - 核心状态
    @Published private(set) var activeWebViews: [UUID: WKWebView] = [:]
    @Published private(set) var memoryUsage: UInt64 = 0
    @Published private(set) var networkQuality: NetworkQuality = .fair
    @Published private(set) var currentMetrics = BrowserMetrics()
    @Published private(set) var isNetworkAvailable = false
    @Published private(set) var connectionType: ConnectionType = .unknown
    @Published private(set) var networkStats = NetworkStats()
    
    // MARK: - 服务依赖
    private let logger = Logger(subsystem: "com.cop.browser", category: "BrowserManager")
    private let securityService = SecurityService.shared
    
    // MARK: - 配置管理
    private let baseConfiguration: WKWebViewConfiguration
    
    // MARK: - 网络监控
    private let pathMonitor = NWPathMonitor()
    private let monitorQueue = DispatchQueue(label: "NetworkMonitor", qos: .utility)
    
    // MARK: - 性能监控
    private var memoryCheckTimer: Timer?
    private var isMonitoring = false
    
    private init() {
        // 创建基础WebView配置
        let config = WKWebViewConfiguration()
        
        // 媒体配置 - 禁用自动播放
        config.allowsInlineMediaPlayback = true
        config.allowsAirPlayForMediaPlayback = true
        config.allowsPictureInPictureMediaPlayback = true
        config.mediaTypesRequiringUserActionForPlayback = [.video, .audio]
        
        // 安全配置
        config.preferences.javaScriptCanOpenWindowsAutomatically = false
        config.preferences.isFraudulentWebsiteWarningEnabled = true
        config.preferences.minimumFontSize = 9.0
        
        // 系统配置
        config.processPool = WKProcessPool()
        config.websiteDataStore = WKWebsiteDataStore.default()
        config.selectionGranularity = .character
        config.dataDetectorTypes = [.phoneNumber, .link, .address]
        config.applicationNameForUserAgent = "cop Browser"
        config.upgradeKnownHostsToHTTPS = false
        
        // 权限配置
        if #available(iOS 14.0, *) {
            config.limitsNavigationsToAppBoundDomains = false
        }
        
        // 添加禁用自动播放的JavaScript
        let disableAutoplayScript = """
        (function() {
            function disableAutoplay() {
                const videos = document.querySelectorAll('video');
                videos.forEach(video => {
                    video.autoplay = false;
                    video.muted = false;
                    if (video.hasAttribute('autoplay')) {
                        video.removeAttribute('autoplay');
                    }
                });
            }
            
            if (document.readyState === 'loading') {
                document.addEventListener('DOMContentLoaded', disableAutoplay);
            } else {
                disableAutoplay();
            }
            
            const observer = new MutationObserver(function(mutations) {
                mutations.forEach(function(mutation) {
                    mutation.addedNodes.forEach(function(node) {
                        if (node.nodeType === 1 && node.tagName === 'VIDEO') {
                            node.autoplay = false;
                            node.muted = false;
                            if (node.hasAttribute('autoplay')) {
                                node.removeAttribute('autoplay');
                            }
                        }
                    });
                });
            });
            
            observer.observe(document.body || document.documentElement, {
                childList: true,
                subtree: true
            });
        })();
        """
        
        let userScript = WKUserScript(
            source: disableAutoplayScript,
            injectionTime: .atDocumentStart,
            forMainFrameOnly: false
        )
        config.userContentController.addUserScript(userScript)
        
        self.baseConfiguration = config
        setupNetworkMonitoring()
        startMonitoring()
        logger.info("✅ BrowserManager 初始化完成")
    }
    
    deinit {
        stopMonitoring()
        pathMonitor.cancel()
    }

    // MARK: - 公共接口

    /// 创建WebView - 统一入口点
    func createWebView(for tab: NewBrowserTab, userAgent: String) -> WKWebView {
        // 复制基础配置，避免共享冲突
        let configuration = baseConfiguration.copy() as! WKWebViewConfiguration

        // 应用安全配置
        securityService.applySecurityConfiguration(to: configuration)

        // 创建WebView
        let webView = WKWebView(frame: .zero, configuration: configuration)

        // 配置WebView属性
        configureWebView(webView, userAgent: userAgent)

        // 注册管理
        activeWebViews[tab.id] = webView
        updateMetrics()

        logger.info("🚀 WebView创建: \(tab.id.uuidString.prefix(8))")
        return webView
    }

    /// 清理WebView - 统一处理
    func cleanupWebView(_ webView: WKWebView, for tabId: UUID) {
        webView.stopLoading()

        // 清理资源
        Task {
            await clearWebViewMemory(webView)
        }

        // 移除管理
        activeWebViews.removeValue(forKey: tabId)
        updateMetrics()

        logger.info("🧹 WebView清理: \(tabId.uuidString.prefix(8))")
    }

    /// 加载URL - 统一处理
    func loadURL(_ url: URL, in webView: WKWebView, for tab: NewBrowserTab) {
        // 安全URL处理
        let secureURL = securityService.enforceHTTPS(for: url)

        // 创建优化请求
        let request = createNetworkRequest(for: secureURL)

        // 加载
        webView.load(request)

        logger.info("🌐 URL加载: \(secureURL.absoluteString.prefix(50))...")
    }

    /// 统一URL处理
    func processURLInput(_ input: String) -> URL? {
        let trimmedInput = input.trimmingCharacters(in: .whitespacesAndNewlines)
        guard !trimmedInput.isEmpty else { return nil }

        // 1. 完整URL检查
        if let url = URL(string: trimmedInput), url.scheme != nil {
            return securityService.enforceHTTPS(for: url)
        }

        // 2. 域名格式检查
        if isValidDomain(trimmedInput) {
            if let url = URL(string: "https://\(trimmedInput)") {
                return securityService.enforceHTTPS(for: url)
            }
        }

        // 3. 搜索查询
        let query = trimmedInput.addingPercentEncoding(withAllowedCharacters: .urlQueryAllowed) ?? ""
        return URL(string: "https://www.google.com/search?q=\(query)")
    }

    /// 统一URL安全验证
    func validateURLSecurity(_ url: URL) -> Bool {
        return !securityService.detectAdvancedThreats(url: url)
    }

    /// 内存优化
    func optimizeMemory() async {
        let usage = getCurrentMemoryUsage()
        memoryUsage = usage
        currentMetrics.memoryUsage = usage

        // 简单的内存压力检测
        let threshold: UInt64 = 300 * 1024 * 1024 // 300MB for iPad mini A17 Pro
        if usage > threshold {
            await performMemoryCleanup()
        }
    }

    /// 网络质量检测
    func checkNetworkQuality() async -> NetworkQuality {
        return networkQuality
    }

    /// 记录网络请求统计
    func recordNetworkRequest(success: Bool, responseTime: TimeInterval) {
        networkStats.totalRequests += 1
        if success {
            networkStats.successfulRequests += 1
        }
        networkStats.averageResponseTime = ((networkStats.averageResponseTime * Double(networkStats.totalRequests - 1)) + responseTime) / Double(networkStats.totalRequests)
        networkStats.successRate = Double(networkStats.successfulRequests) / Double(networkStats.totalRequests)

        currentMetrics.networkRequests = networkStats.totalRequests
    }

    // MARK: - 网络错误处理

    /// 统一网络错误处理
    func handleNetworkError(_ error: Error, context: String = "", webView: WKWebView? = nil) {
        logger.error("🚨 网络错误: \(error.localizedDescription) - 上下文: \(context)")

        currentMetrics.errorCount += 1

        // 如果有WebView，显示错误页面
        if let webView = webView {
            Task { @MainActor in
                await self.handleWebViewError(error, webView: webView)
            }
        }
    }

    /// 创建优化的网络请求
    func createNetworkRequest(for url: URL) -> URLRequest {
        var request = URLRequest(url: url)

        // 根据网络质量调整超时和缓存策略
        switch networkQuality {
        case .excellent:
            request.timeoutInterval = 15.0
            request.cachePolicy = .useProtocolCachePolicy
        case .good:
            request.timeoutInterval = 20.0
            request.cachePolicy = .returnCacheDataElseLoad
        case .fair:
            request.timeoutInterval = 30.0
            request.cachePolicy = .returnCacheDataElseLoad
        case .poor:
            request.timeoutInterval = 45.0
            request.cachePolicy = .returnCacheDataDontLoad
        }

        // 添加基础请求头
        request.setValue("cop Browser/1.0", forHTTPHeaderField: "User-Agent")
        request.setValue("gzip, deflate, br", forHTTPHeaderField: "Accept-Encoding")
        request.setValue("text/html,application/xhtml+xml,application/xml;q=0.9,*/*;q=0.8", forHTTPHeaderField: "Accept")

        return request
    }

    // MARK: - 监控和性能管理

    /// 开始性能监控
    func startMonitoring() {
        guard !isMonitoring else { return }
        isMonitoring = true

        // 内存检查 - 每30秒
        memoryCheckTimer = Timer.scheduledTimer(withTimeInterval: 30.0, repeats: true) { [weak self] _ in
            Task { @MainActor in
                await self?.optimizeMemory()
            }
        }

        logger.info("🔄 开始性能监控")
    }

    /// 停止性能监控
    func stopMonitoring() {
        isMonitoring = false
        memoryCheckTimer?.invalidate()
        memoryCheckTimer = nil
        logger.info("⏹️ 停止性能监控")
    }

    // MARK: - 私有方法

    private func setupNetworkMonitoring() {
        pathMonitor.pathUpdateHandler = { [weak self] path in
            Task { @MainActor in
                self?.updateNetworkStatus(path)
            }
        }
        pathMonitor.start(queue: monitorQueue)
    }

    private func updateNetworkStatus(_ path: NWPath) {
        isNetworkAvailable = (path.status == .satisfied)

        // 检测连接类型
        if path.usesInterfaceType(.cellular) {
            connectionType = .cellular
        } else if path.usesInterfaceType(.wifi) {
            connectionType = .wifi
        } else if path.usesInterfaceType(.wiredEthernet) {
            connectionType = .ethernet
        } else {
            connectionType = .unknown
        }

        // 简单的网络质量评估
        if !isNetworkAvailable {
            networkQuality = .poor
        } else if connectionType == .wifi || connectionType == .ethernet {
            networkQuality = .excellent
        } else if connectionType == .cellular && !path.isExpensive {
            networkQuality = .good
        } else {
            networkQuality = .fair
        }

        logger.info("🌐 网络状态更新: \(connectionType.displayName), 质量: \(networkQuality.description)")
    }

    private func updateMetrics() {
        currentMetrics.activeWebViews = activeWebViews.count
        currentMetrics.memoryUsage = getCurrentMemoryUsage()
    }

    private func configureWebView(_ webView: WKWebView, userAgent: String) {
        // 用户代理
        webView.customUserAgent = userAgent

        // 基础属性
        webView.isOpaque = false
        webView.backgroundColor = UIColor.clear
        webView.allowsLinkPreview = true
        webView.allowsBackForwardNavigationGestures = true

        // 滚动优化
        let scrollView = webView.scrollView
        scrollView.showsVerticalScrollIndicator = true
        scrollView.showsHorizontalScrollIndicator = false
        scrollView.delaysContentTouches = false
        scrollView.canCancelContentTouches = true

        // iOS 16+ 优化
        if #available(iOS 16.0, *) {
            scrollView.keyboardDismissMode = .onDrag
        }

        // 应用安全服务的Cookie策略
        securityService.applyCookiePolicy(to: webView)
    }

    private func handleWebViewError(_ error: Error, webView: WKWebView) async {
        let nsError = error as NSError

        switch nsError.code {
        case NSURLErrorNotConnectedToInternet, NSURLErrorNetworkConnectionLost:
            await showOfflineErrorPage(webView: webView)
        case NSURLErrorTimedOut:
            await showTimeoutErrorPage(webView: webView)
        default:
            break
        }
    }

    private func showOfflineErrorPage(webView: WKWebView) async {
        let errorHTML = """
        <!DOCTYPE html>
        <html>
        <head>
            <meta charset="UTF-8">
            <meta name="viewport" content="width=device-width, initial-scale=1.0">
            <title>网络连接错误</title>
            <style>
                body { font-family: -apple-system, BlinkMacSystemFont, sans-serif; text-align: center; padding: 40px; }
                .error-icon { font-size: 64px; margin-bottom: 20px; }
                .error-title { font-size: 24px; font-weight: bold; margin-bottom: 10px; }
                .error-message { font-size: 16px; color: #666; margin-bottom: 30px; }
                .retry-button { background: #007AFF; color: white; border: none; padding: 12px 24px; border-radius: 8px; font-size: 16px; cursor: pointer; }
            </style>
        </head>
        <body>
            <div class="error-icon">📡</div>
            <div class="error-title">网络连接失败</div>
            <div class="error-message">请检查您的网络连接后重试</div>
            <button class="retry-button" onclick="window.location.reload()">重新加载</button>
        </body>
        </html>
        """
        webView.loadHTMLString(errorHTML, baseURL: nil)
    }

    private func showTimeoutErrorPage(webView: WKWebView) async {
        let errorHTML = """
        <!DOCTYPE html>
        <html>
        <head>
            <meta charset="UTF-8">
            <meta name="viewport" content="width=device-width, initial-scale=1.0">
            <title>连接超时</title>
            <style>
                body { font-family: -apple-system, BlinkMacSystemFont, sans-serif; text-align: center; padding: 40px; }
                .error-icon { font-size: 64px; margin-bottom: 20px; }
                .error-title { font-size: 24px; font-weight: bold; margin-bottom: 10px; }
                .error-message { font-size: 16px; color: #666; margin-bottom: 30px; }
                .retry-button { background: #007AFF; color: white; border: none; padding: 12px 24px; border-radius: 8px; font-size: 16px; cursor: pointer; }
            </style>
        </head>
        <body>
            <div class="error-icon">⏱️</div>
            <div class="error-title">连接超时</div>
            <div class="error-message">服务器响应时间过长，请稍后重试</div>
            <button class="retry-button" onclick="window.location.reload()">重新加载</button>
        </body>
        </html>
        """
        webView.loadHTMLString(errorHTML, baseURL: nil)
    }

    private func isValidDomain(_ input: String) -> Bool {
        let domainRegex = "^([a-zA-Z0-9]([a-zA-Z0-9\\-]{0,61}[a-zA-Z0-9])?\\.)+(com|net|org|edu|gov|mil|biz|info|mobi|name|aero|jobs|museum|[a-z]{2})$"
        let domainPredicate = NSPredicate(format: "SELF MATCHES %@", domainRegex)
        return domainPredicate.evaluate(with: input.lowercased())
    }

    private func getCurrentMemoryUsage() -> UInt64 {
        var info = mach_task_basic_info()
        var count = mach_msg_type_number_t(MemoryLayout<mach_task_basic_info>.size)/4

        let kerr: kern_return_t = withUnsafeMutablePointer(to: &info) {
            $0.withMemoryRebound(to: integer_t.self, capacity: 1) {
                task_info(mach_task_self_, task_flavor_t(MACH_TASK_BASIC_INFO), $0, &count)
            }
        }

        if kerr == KERN_SUCCESS {
            return info.resident_size
        } else {
            return 0
        }
    }

    private func clearWebViewMemory(_ webView: WKWebView) async {
        // 清理WebView内存
        await webView.configuration.websiteDataStore.removeData(
            ofTypes: WKWebsiteDataStore.allWebsiteDataTypes(),
            modifiedSince: Date.distantPast
        )
    }

    private func performMemoryCleanup() async {
        logger.info("🧹 执行内存清理")

        // 清理不活跃的WebView
        let inactiveWebViews = activeWebViews.filter { _, webView in
            webView.superview == nil
        }

        for (tabId, webView) in inactiveWebViews {
            await clearWebViewMemory(webView)
            activeWebViews.removeValue(forKey: tabId)
        }

        // 强制垃圾回收
        autoreleasepool {
            // 触发内存回收
        }

        updateMetrics()
    }
}
